<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>jQuery Stepper</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .step {
            display: none;
        }
        .step.active {
            display: block;
        }
        .button {
            margin-top: 10px;
        }
    </style>
</head>
<body>

<div class="step active" id="step-1">
    <h2>Step 1</h2>
    <p>This is the content of step 1.</p>
    <button class="button next">Next</button>
</div>

<div class="step" id="step-2">
    <h2>Step 2</h2>
    <p>This is the content of step 2.</p>
    <button class="button prev">Previous</button>
    <button class="button next">Next</button>
</div>

<div class="step" id="step-3">
    <h2>Step 3</h2>
    <p>This is the content of step 3.</p>
    <button class="button prev">Previous</button>
    <button class="button next">Next</button>
</div>

<div class="step" id="step-4">
    <h2>Step 4</h2>
    <p>This is the content of step 4.</p>
    <button class="button prev">Previous</button>
    <button class="button submit">Submit</button>
</div>

<script>
$(document).ready(function() {
    var currentStep = 1;
    var totalSteps = 4;

    function showStep(step) {
        $('.step').removeClass('active');
        $('#step-' + step).addClass('active');
    }

    $('.next').click(function() {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
        }
    });

    $('.prev').click(function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    $('.submit').click(function() {
        alert('Form submitted!');
    });
});
</script>

</body>
</html>
