<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Session;

class StepperController extends Controller
{
    public function storeStepperData(Request $request)
    {
        // Store the data in the session to handle users leaving midway
        $stepData = $request->except('_token');
        session(['stepData' => $stepData]);

        // If the user is on the final step, save everything to the database
        if ($request->isMethod('post')) {
            // Assuming you have a model like User or FormSubmissions to store data
            // Example: FormSubmission::create($stepData);
            Session::forget('stepData'); // Clear the session after final submission
            return redirect()->back()->with('success', 'Form submitted successfully!');
        }

        return redirect()->back();
    }
}
