<?php

use App\Http\Controllers\StepperController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UploadController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});


Route::view("stepper", "stepper");
Route::post('/upload-temp-files', [UploadController::class, 'upload'])->name('uploadTempFiles');
Route::post('/store-stepper-data', [StepperController::class, 'storeStepperData'])->name('storeStepperData');
