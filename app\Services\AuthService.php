<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AuthService
{
    private function getPrivateKey()
    {
        $privateKey = file_get_contents(storage_path('app/keys/Private Key.txt'));
        if (!$privateKey) {
            throw new \Exception('Could not read private key file');
        }
        return $privateKey;
    }

    public function user_generateJwt(): string
    {
        $privateKey = $this->getPrivateKey();
        $kid = "taxformhero2024";
        
        $header = [
            'alg' => 'RS256',
            'kid' => $kid,
            'typ' => 'JWT'
        ];
        
        $payload = [
            'aud' => 'https://api.alt.www4.irs.gov/auth/oauth/v2/token',
            'iss' => '578fef67-fa88-40b3-80ad-aaaace59e06b',
            'sub' => 'WYX74X077H-14704980', // User ID - different from client ID
            'iat' => time(),
            'exp' => time() + 900,
            'jti' => bin2hex(random_bytes(16)),
        ];

        return JWT::encode($payload, $privateKey, 'RS256', $kid, $header);
    }

    public function client_generateJwt(): string
    {
        $privateKey = $this->getPrivateKey();
        $kid = "taxformhero2024";
        
        $header = [
            'alg' => 'RS256',
            'kid' => $kid,
            'typ' => 'JWT'
        ];
        
        $payload = [
            'aud' => 'https://api.alt.www4.irs.gov/auth/oauth/v2/token',
            'iss' => '578fef67-fa88-40b3-80ad-aaaace59e06b',
            'sub' => '578fef67-fa88-40b3-80ad-aaaace59e06b', // For client JWT, sub = iss
            'iat' => time(),
            'exp' => time() + 900,
            'jti' => bin2hex(random_bytes(16)),
        ];

        return JWT::encode($payload, $privateKey, 'RS256', $kid, $header);
    }

    /**
     * Get bearer token as string - NOT a Laravel Response
     */
    public function bearer_token(): string
    {
        $user_jwt = $this->user_generateJwt();
        $client_jwt = $this->client_generateJwt();
        
        Log::info('Requesting OAuth token');
        
        $response = Http::asForm()
            ->withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json'
            ])
            ->post('https://api.alt.www4.irs.gov/auth/oauth/v2/token', [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $user_jwt,
                'client_assertion_type' => 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
                'client_assertion' => $client_jwt
            ]);

        if (!$response->successful()) {
            Log::error('OAuth token request failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            throw new \Exception('OAuth request failed: ' . $response->status());
        }

        $data = $response->json();
        
        if (!isset($data['access_token'])) {
            Log::error('No access token in response', ['data' => $data]);
            throw new \Exception('No access token in OAuth response');
        }

        $accessToken = $data['access_token'];
        
        Log::info('OAuth token received successfully');
        
        return $accessToken; // Return ONLY the token string
    }
}