<?php

namespace App\Http\Controllers;

use App\Services\AuthService;
use Illuminate\Http\Request;
use SimpleXMLElement;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AcceptanceController extends Controller
{
    protected $authService;

    function __construct()
    {
        $this->authService = new AuthService();
    }

    public function acceptance_post(Request $request)
    {
        // Example dynamic data (you can get this from a form, database, etc.)
        $dynamicData = [
            'UniqueTransmissionId' => Str::uuid().':IRIS:DDKNH::A.', // Generate dynamic UUID
            'TaxYr' => '2024',
            'PriorYearDataInd' => '0',
            'TransmissionTypeCd' => 'O',
            'TestCd' => 'T',
            // Add more dynamic data fields as required...
        ];

        // Start building the XML
        $xml = new SimpleXMLElement('<n1:IRTransmission xmlns:n1="urn:us:gov:treasury:irs:ir" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:us:gov:treasury:irs:ir ../MSG/IRS-IRIntakeTransmissionMessage.xsd">
<n1:IRTransmissionManifest>
<n1:UniqueTransmissionId>07000090-0ll0-0000-0000-*********000:IRIS:DDKNH::A.</n1:UniqueTransmissionId>
<n1:TaxYr>2024</n1:TaxYr>
<n1:PriorYearDataInd>0</n1:PriorYearDataInd>
<n1:TransmissionTypeCd>O</n1:TransmissionTypeCd>
<n1:TestCd>T</n1:TestCd>
<n1:TransmitterGrp>
<n1:TIN>*********</n1:TIN>
<n1:TINSubmittedTypeCd>INDIVIDUAL_TIN</n1:TINSubmittedTypeCd>
<n1:TransmitterControlCd>00000</n1:TransmitterControlCd>
<n1:ForeignEntityInd>0</n1:ForeignEntityInd>
<n1:PersonNm>John Smith</n1:PersonNm>
<n1:CompanyGrp>
<n1:BusinessName>
<n1:BusinessNameLine1Txt>Tax 4 You</n1:BusinessNameLine1Txt>
<n1:BusinessNameLine2Txt>DBA</n1:BusinessNameLine2Txt>
</n1:BusinessName>
<n1:MailingAddressGrp>
<n1:USAddress>
<n1:AddressLine1Txt>9 August St</n1:AddressLine1Txt>
<n1:AddressLine2Txt>Bell Tower</n1:AddressLine2Txt>
<n1:CityNm>Jones</n1:CityNm>
<n1:StateAbbreviationCd>WA</n1:StateAbbreviationCd>
<n1:ZIPCd>00000</n1:ZIPCd>
</n1:USAddress>
</n1:MailingAddressGrp>
</n1:CompanyGrp>
<n1:ContactNameGrp>
<n1:PersonNm>Sally Smith</n1:PersonNm>
</n1:ContactNameGrp>
<n1:ContactEmailAddressTxt><EMAIL></n1:ContactEmailAddressTxt>
<n1:ContactPhoneNum>4853431222</n1:ContactPhoneNum>
</n1:TransmitterGrp>
<n1:VendorCd>V</n1:VendorCd>
<n1:SoftwareId>23IM89454T</n1:SoftwareId>
<n1:VendorGrp>
<n1:ForeignEntityInd>0</n1:ForeignEntityInd>
<n1:BusinessName>
<n1:BusinessNameLine1Txt>Tax One</n1:BusinessNameLine1Txt>
<n1:BusinessNameLine2Txt>DBA</n1:BusinessNameLine2Txt>
</n1:BusinessName>
<n1:MailingAddressGrp>
<n1:USAddress>
<n1:AddressLine1Txt>5 George St</n1:AddressLine1Txt>
<n1:AddressLine2Txt>Suite 500</n1:AddressLine2Txt>
<n1:CityNm>Jones</n1:CityNm>
<n1:StateAbbreviationCd>WA</n1:StateAbbreviationCd>
<n1:ZIPCd>00000</n1:ZIPCd>
</n1:USAddress>
</n1:MailingAddressGrp>
<n1:ContactNameGrp>
<n1:PersonNm>Becky Brown</n1:PersonNm>
</n1:ContactNameGrp>
<n1:ContactEmailAddressTxt><EMAIL></n1:ContactEmailAddressTxt>
<n1:ContactPhoneNum>8459012354</n1:ContactPhoneNum>
</n1:VendorGrp>
<n1:TotalIssuerFormCnt>1</n1:TotalIssuerFormCnt>
<n1:TotalRecipientFormCnt>1</n1:TotalRecipientFormCnt>
<n1:PaperSubmissionInd>0</n1:PaperSubmissionInd>
<n1:MediaSourceCd>M</n1:MediaSourceCd>
<n1:SubmissionChannelCd>A2A</n1:SubmissionChannelCd>
</n1:IRTransmissionManifest>
<n1:IRSubmission1Grp>
<n1:IRSubmission1Header>
<n1:SubmissionId>1</n1:SubmissionId>
<n1:TaxYr>2024</n1:TaxYr>
<n1:IssuerDetail>
<n1:ForeignEntityInd>0</n1:ForeignEntityInd>
<n1:TIN>*********</n1:TIN>
<n1:TINSubmittedTypeCd>INDIVIDUAL_TIN</n1:TINSubmittedTypeCd>
<n1:PersonNameControlTxt>PAYS</n1:PersonNameControlTxt>
<n1:PersonName>
<n1:PersonFirstNm>Jane</n1:PersonFirstNm>
<n1:PersonMiddleNm>B</n1:PersonMiddleNm>
<n1:PersonLastNm>Payson</n1:PersonLastNm>
</n1:PersonName>
<n1:MailingAddressGrp>
<n1:USAddress>
<n1:AddressLine1Txt>200 West Main St</n1:AddressLine1Txt>
<n1:CityNm>Boston</n1:CityNm>
<n1:StateAbbreviationCd>MA</n1:StateAbbreviationCd>
<n1:ZIPCd>00000</n1:ZIPCd>
</n1:USAddress>
</n1:MailingAddressGrp>
<n1:PhoneNum>7905648450</n1:PhoneNum>
</n1:IssuerDetail>
<n1:ContactPersonInformationGrp>
<n1:ContactPersonNm>Mary White</n1:ContactPersonNm>
<n1:ContactPhoneNum>3348884564</n1:ContactPhoneNum>
<n1:ContactEmailAddressTxt><EMAIL></n1:ContactEmailAddressTxt>
<n1:ContactFaxNum>4849954509</n1:ContactFaxNum>
</n1:ContactPersonInformationGrp>
<n1:FormTypeCd>1099MISC</n1:FormTypeCd>
<n1:ParentFormTypeCd>1096</n1:ParentFormTypeCd>
<n1:CFSFElectionInd>0</n1:CFSFElectionInd>
<n1:JuratSignatureGrp>
<n1:SignatureIntentInd>1</n1:SignatureIntentInd>
<n1:JuratSignaturePIN>00000</n1:JuratSignaturePIN>
<n1:SignatureDt>2024-04-04</n1:SignatureDt>
<n1:JuratPersonTitleTxt>CFO</n1:JuratPersonTitleTxt>
<n1:PersonNm>Hong Li</n1:PersonNm>
</n1:JuratSignatureGrp>
<n1:TotalReportedRcpntFormCnt>1</n1:TotalReportedRcpntFormCnt>
<n1:IRSubmission1FormTotals>
<n1:Form1099MISCTotalAmtGrp>
<n1:FederalIncomeTaxWithheldAmt>2000.00</n1:FederalIncomeTaxWithheldAmt>
<n1:TotalReportedAmt>38000.92</n1:TotalReportedAmt>
<n1:RentAmt>3000.5</n1:RentAmt>
<n1:RoyaltyAmt>10000.20</n1:RoyaltyAmt>
<n1:OtherIncomeAmt>20000</n1:OtherIncomeAmt>
<n1:FishingBoatProceedsAmt>5000.22</n1:FishingBoatProceedsAmt>
</n1:Form1099MISCTotalAmtGrp>
<n1:Form1099MISCTotalByStateGrp>
<n1:StateAbbreviationCd>LA</n1:StateAbbreviationCd>
<n1:TotalReportedRcpntFormCnt>1</n1:TotalReportedRcpntFormCnt>
<n1:FederalIncomeTaxWithheldAmt>2000.00</n1:FederalIncomeTaxWithheldAmt>
<n1:StateTaxWithheldAmt>200.00</n1:StateTaxWithheldAmt>
<n1:LocalTaxWithheldAmt>0</n1:LocalTaxWithheldAmt>
<n1:RentAmt>1500.25</n1:RentAmt>
<n1:RoyaltyAmt>5000.1</n1:RoyaltyAmt>
<n1:OtherIncomeAmt>10000</n1:OtherIncomeAmt>
<n1:FishingBoatProceedsAmt>5000.22</n1:FishingBoatProceedsAmt>
</n1:Form1099MISCTotalByStateGrp>
<n1:Form1099MISCTotalByStateGrp>
<n1:StateAbbreviationCd>ID</n1:StateAbbreviationCd>
<n1:TotalReportedRcpntFormCnt>1</n1:TotalReportedRcpntFormCnt>
<n1:FederalIncomeTaxWithheldAmt>2000.00</n1:FederalIncomeTaxWithheldAmt>
<n1:StateTaxWithheldAmt>100.00</n1:StateTaxWithheldAmt>
<n1:LocalTaxWithheldAmt>0</n1:LocalTaxWithheldAmt>
<n1:RentAmt>1500.25</n1:RentAmt>
<n1:RoyaltyAmt>5000.1</n1:RoyaltyAmt>
<n1:OtherIncomeAmt>10000</n1:OtherIncomeAmt>
<n1:FishingBoatProceedsAmt>0</n1:FishingBoatProceedsAmt>
</n1:Form1099MISCTotalByStateGrp>
</n1:IRSubmission1FormTotals>
</n1:IRSubmission1Header>
<n1:IRSubmission1Detail>
<n1:Form1099MISCDetail>
<n1:TaxYr>2024</n1:TaxYr>
<n1:RecordId>1</n1:RecordId>
<n1:CFSFElectionStateCd>LA</n1:CFSFElectionStateCd>
<n1:CFSFElectionStateCd>ID</n1:CFSFElectionStateCd>
<n1:VoidInd>0</n1:VoidInd>
<n1:CorrectedInd>0</n1:CorrectedInd>
<n1:RecipientDetail>
<n1:TIN>*********</n1:TIN>
<n1:TINSubmittedTypeCd>INDIVIDUAL_TIN</n1:TINSubmittedTypeCd>
<n1:PersonNameControlTxt>PORT</n1:PersonNameControlTxt>
<n1:PersonName>
<n1:PersonFirstNm>Jerry</n1:PersonFirstNm>
<n1:PersonMiddleNm>L</n1:PersonMiddleNm>
<n1:PersonLastNm>Porter</n1:PersonLastNm>
</n1:PersonName>
<n1:MailingAddressGrp>
<n1:USAddress>
<n1:AddressLine1Txt>6 Alpha St</n1:AddressLine1Txt>
<n1:CityNm>Boise</n1:CityNm>
<n1:StateAbbreviationCd>ID</n1:StateAbbreviationCd>
<n1:ZIPCd>00000</n1:ZIPCd>
</n1:USAddress>
</n1:MailingAddressGrp>
</n1:RecipientDetail>
<n1:SecondTINNoticeInd>0</n1:SecondTINNoticeInd>
<n1:FATCAFilingRequirementInd>0</n1:FATCAFilingRequirementInd>
<n1:RentAmt>3000.5</n1:RentAmt>
<n1:RoyaltyAmt>10000.20</n1:RoyaltyAmt>
<n1:OtherIncomeAmt>20000</n1:OtherIncomeAmt>
<n1:FederalIncomeTaxWithheldAmt>2000</n1:FederalIncomeTaxWithheldAmt>
<n1:FishingBoatProceedsAmt>5000.22</n1:FishingBoatProceedsAmt>
<n1:DirectSaleAboveThresholdInd>0</n1:DirectSaleAboveThresholdInd>
<n1:StateLocalTaxGrp>
<n1:StateAbbreviationCd>LA</n1:StateAbbreviationCd>
<n1:StateTaxGrp>
<n1:StateIdNum>488IKED</n1:StateIdNum>
<n1:StateTaxWithheldAmt>200</n1:StateTaxWithheldAmt>
<n1:StateIncomeAmt>21500.47</n1:StateIncomeAmt>
<n1:StateDistributionAmt>0</n1:StateDistributionAmt>
</n1:StateTaxGrp>
</n1:StateLocalTaxGrp>
<n1:StateLocalTaxGrp>
<n1:StateAbbreviationCd>ID</n1:StateAbbreviationCd>
<n1:StateTaxGrp>
<n1:StateIdNum>4UR85I3</n1:StateIdNum>
<n1:StateTaxWithheldAmt>100</n1:StateTaxWithheldAmt>
<n1:StateIncomeAmt>16500.35</n1:StateIncomeAmt>
<n1:StateDistributionAmt>0</n1:StateDistributionAmt>
</n1:StateTaxGrp>
<n1:SpecialDataEntriesTxt>none</n1:SpecialDataEntriesTxt>
</n1:StateLocalTaxGrp>
</n1:Form1099MISCDetail>
</n1:IRSubmission1Detail>
</n1:IRSubmission1Grp>
</n1:IRTransmission>');
        // Directly update the existing XML values
        
        $xml->n1->IRTransmissionManifest->n1->UniqueTransmissionId = $dynamicData['UniqueTransmissionId'];
        $xml->n1->IRTransmissionManifest->n1->TaxYr = $dynamicData['TaxYr'];
        $xml->n1->IRTransmissionManifest->n1->PriorYearDataInd = $dynamicData['PriorYearDataInd'];
        $xml->n1->IRTransmissionManifest->n1->TransmissionTypeCd = $dynamicData['TransmissionTypeCd'];
        $xml->n1->IRTransmissionManifest->n1->TestCd = $dynamicData['TestCd'];

        // Convert the generated XML to a string
        $xmlContent = $xml->asXML();
        return $xmlContent;
        // API test endpoint and Bearer token
        $url = 'https://api.alt.www4.irs.gov/IRIntakeAcceptanceA2A/1.0/irisa2a/v1/intake-acceptance'; // Test endpoint
        $accessToken = $this->authService->bearer_token(); // Replace with your access token

        // Send the request
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
        ])
        ->attach('file', $xmlContent, 'dynamic-file.xml')
        ->post($url, [
            'Content-Type' => 'application/xml',
            'Accept' => 'application/xml',
        ]);

        // Handle response
        if ($response->successful()) {
            return response()->json(['message' => 'Request successful', 'data' => $response->json()]);
        } else {
            return response()->json(['message' => 'Request failed', 'error' => $response->body()], 500);
        }
    }
}
