<?php

use App\Http\Controllers\AcceptanceController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\IRSAuthController;
use App\Http\Controllers\IRSController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('client-jwt', [IRSController::class, 'client_generateJwt']);
Route::get('user-jwt', [IRSController::class, 'user_generateJwt']);
Route::get('access-token', [IRSController::class, 'getAccessToken']);


// New OAuth 2.0 authorization code flow routes
Route::get('authorize', [IRSController::class, 'authorizeUser']);
Route::get('auth/callback', [IRSController::class, 'handleCallback']);
Route::get('oauth-token', [IRSController::class, 'getOAuth2AccessToken']);


// Certificate verification route
Route::get('verify-certificate', [IRSController::class, 'verifyCertificate']);

Route::post('acceptance-post', [AcceptanceController::class, 'acceptance_post']);