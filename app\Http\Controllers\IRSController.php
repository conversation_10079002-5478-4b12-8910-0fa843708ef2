<?php

namespace App\Http\Controllers;

use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class IRSController extends Controller
{
    public function client_generateJwt()
    {
        $now = time();

        // Option 1: Read private key from file
        $privateKey = file_get_contents(storage_path('app/keys/Private Key.txt'));
        $kid = "taxformhero2024";
        //$kid = "hero20241099";
        $header = [
            'alg' => 'RS256',
            'kid' => $kid,
            "typ" => "JWT"
        ];
        $clientpayload = [
            'aud' => 'https://api.alt.www4.irs.gov/auth/oauth/v2/token',  // Test environment URL
            'iss' => "578fef67-fa88-40b3-80ad-aaaace59e06b",  // Client ID
            'sub' => "578fef67-fa88-40b3-80ad-aaaace59e06b",  // For client JWT, sub = iss (Client ID)
            'iat' => time(),
            'exp' => time() + 900,
            // 'iat' => $now,
            // 'exp' => $now + 900,
            'jti' => bin2hex(random_bytes(16)),
        ];

        return JWT::encode($clientpayload, $privateKey, 'RS256', $kid, $header);
    }
    public function user_generateJwt()
    {
        $now = time();
        // Use the same private key
        $privateKey = file_get_contents(storage_path('app/keys/Private Key.txt'));

        $kid = "taxformhero2024";
        //$kid = "hero20241099";
        $header = [
            'alg' => 'RS256',
            'kid' => $kid,
            "typ" => "JWT"
        ];
        $userpayload = [
            'aud' => 'https://api.alt.www4.irs.gov/auth/oauth/v2/token',  // Test environment URL
            'iss' => "578fef67-fa88-40b3-80ad-aaaace59e06b",  // Client ID
            'sub' => "WYX74X077H-14704980",  // User ID - must be different from client ID
            'iat' => time(),
            'exp' => time() + 900,
            // 'iat' => $now,
            // 'exp' => $now + 900,
            'jti' => bin2hex(random_bytes(16)),
        ];

        return JWT::encode($userpayload, $privateKey, 'RS256', $kid, $header);
    }


    /**
     * Function to request the Bearer Token from IRS API
     */
    public function getAccessToken(Request $request)
    {
        try {
            // Step 1: Generate the JWT
            $user_jwt = $this->user_generateJwt();
            $client_jwt = $this->client_generateJwt();
            // Step 2: Make the HTTP request to the IRS token endpoint using Laravel's HTTP client
            $response = Http::asForm()  // Send as form-encoded data
                ->withHeaders([
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json'
                ])
                ->post('https://api.alt.www4.irs.gov/auth/oauth/v2/token', [
                    'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                    'assertion' => $user_jwt,  // JWT with user identity in sub claim
                    'client_assertion_type' => 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
                    'client_assertion' => $client_jwt // The client assertion JWT
                ]);

            // Step 3: Check if the response was successful and handle the access token
            if ($response->successful() && $response->json('access_token')) {
                return response()->json($response->json(), 200);
            } else {
                // If the response fails, return an error message
                return response()->json([
                    'error' => 'Unable to retrieve access token',
                    'response' => $response->json(),
                ], 400);
            }
        } catch (\Exception $e) {
            // Log and return the error
            Log::error('Error getting access token: ' . $e->getMessage());
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Function to request the Bearer Token using OAuth 2.0 authorization code flow
     */
    public function getOAuth2AccessToken(Request $request)
    {
        try {
            // Generate client assertion JWT
            $client_jwt = $this->client_generateJwt();

            // Check if we have an authorization code
            $auth_code = $request->input('code');

            if (!$auth_code) {
                return response()->json([
                    'error' => 'Missing authorization code',
                    'message' => 'Authorization code is required for OAuth 2.0 flow'
                ], 400);
            }

            // Make the HTTP request to the IRS token endpoint using authorization code flow
            $response = Http::asForm()
                ->withHeaders([
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json'
                ])
                ->post('https://api.alt.www4.irs.gov/auth/oauth/v2/token', [
                    'grant_type' => 'authorization_code',
                    'code' => $auth_code,
                    'client_assertion_type' => 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
                    'client_assertion' => $client_jwt
                ]);

            // Handle response
            if ($response->successful() && $response->json('access_token')) {
                return response()->json([
                    'access_token' => $response->json('access_token'),
                    'expires_in' => $response->json('expires_in'),
                    'token_type' => $response->json('token_type'),
                    'id_token' => $response->json('id_token'),
                ], 200);
            } else {
                Log::error('IRS OAuth API Error: ' . json_encode($response->json()));
                return response()->json([
                    'error' => 'Unable to retrieve OAuth access token',
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Error getting OAuth access token: ' . $e->getMessage());
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Redirect user to IRS authorization endpoint for OAuth 2.0 flow
     */
    public function authorizeUser()
    {
        $client_id = "578fef67-fa88-40b3-80ad-aaaace59e06b";
        $redirect_uri = url('/api/auth/callback'); // Make sure this matches exactly what's registered with IRS
        $state = bin2hex(random_bytes(16)); // Generate a random state parameter

        // Store state in session to verify when the user returns
        session(['oauth_state' => $state]);

        // Build the authorization URL - use the correct environment (test or production)
        $auth_url = "https://api.alt.www4.irs.gov/auth/oauth/v2/authorize?" . http_build_query([
            'client_id' => $client_id,
            'response_type' => 'code',
            'redirect_uri' => $redirect_uri,
            'state' => $state,
            'scope' => 'openid' // Adjust scopes as needed
        ]);

        // Log the URL for debugging
        Log::info('Redirecting to IRS authorization URL: ' . $auth_url);

        // Redirect the user to the IRS authorization page
        return redirect($auth_url);
    }

    /**
     * Handle the callback from IRS authorization server
     */
    public function handleCallback(Request $request)
    {
        // Verify state parameter to prevent CSRF
        if ($request->input('state') !== session('oauth_state')) {
            return response()->json(['error' => 'Invalid state parameter'], 400);
        }

        // Get the authorization code
        $code = $request->input('code');

        if (!$code) {
            return response()->json(['error' => 'No authorization code received'], 400);
        }

        // Exchange the code for an access token
        return $this->getOAuth2AccessToken($request);
    }

    /**
     * Verify if the certificate is valid for IRS authentication
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyCertificate()
    {
        try {
            // Path to the certificate
            $certPath = storage_path('app/keys/Certificate PEM file.pem');

            // Check if certificate file exists
            if (!file_exists($certPath)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Certificate file not found'
                ], 404);
            }

            // Load certificate
            $cert = file_get_contents($certPath);
            $certInfo = openssl_x509_parse($cert);

            if (!$certInfo) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid certificate format'
                ], 400);
            }

            // Check certificate validity period
            $currentTime = time();
            $validFrom = $certInfo['validFrom_time_t'];
            $validTo = $certInfo['validTo_time_t'];

            $isValid = ($currentTime >= $validFrom && $currentTime <= $validTo);

            // Get certificate thumbprint (SHA-1)
            $thumbprint = openssl_x509_fingerprint($cert, 'sha1');

            return response()->json([
                'status' => $isValid ? 'valid' : 'expired',
                'subject' => $certInfo['subject'],
                'issuer' => $certInfo['issuer'],
                'validFrom' => date('Y-m-d H:i:s', $validFrom),
                'validTo' => date('Y-m-d H:i:s', $validTo),
                'thumbprint' => $thumbprint,
                'x5t' => base64_encode(hex2bin($thumbprint)) // x5t format for JWT header
            ], 200);
        } catch (\Exception $e) {
            Log::error('Certificate verification error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
