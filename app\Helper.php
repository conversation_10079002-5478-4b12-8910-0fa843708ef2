<?php

use Firebase\JWT\JWT;

 function generateJwt()
{
    $now = time();
    $privateKey = file_get_contents(storage_path('app/keys/nazrul_private_key.pem'));
    $client_id = "6164a2e9-a65c-4a40-872a-fcad46a9e45c";
    // $user_id = "WYX74X077H";
    $kid = "dUG7iigYUwcer7Nkt9580OsuCNocHa3TYy_pQyGodyM";
    $payload = [
        'iss' => $client_id,  // Issuer - your client ID | (Issuer): Always the Client ID because your application is issuing the JWT.
        'sub' => $client_id,  // Subject - your client ID | (Subject): Also the Client ID in the A2A flow because your application is the subject of the request.
        'aud' => 'https://api.www4.irs.gov/auth/oauth/v2/token',  // Audience - the IRS token endpoint
        'iat' => $now,  // Issued at time
        'exp' => $now + 900,  // Expiration time (15 minutes)
        'jti' => bin2hex(random_bytes(16)),  // JWT ID for replay attack prevention
    ];
    $header = [
        'alg' => 'RS256',
        'kid' => $kid,
    ];
    return JWT::encode($payload, $privateKey, 'RS256', $kid, $header);
}